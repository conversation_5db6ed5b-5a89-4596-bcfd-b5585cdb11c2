#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
读取同花顺StockBlock.ini文件并解析内容，获取指定板块的股票代码
"""

import os
import re

def read_stock_block_ini(ths_path):
    """读取同花顺StockBlock.ini文件并返回内容"""
    # 拼接完整的文件路径
    file_path = os.path.join(ths_path, "StockBlock.ini")
    
    try:
        with open(file_path, 'r', encoding='gbk') as f:
            content = f.read()
        return content
    except UnicodeDecodeError:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return content
        except Exception as e:
            print(f"读取文件时出错 (UTF-8): {e}")
            return None
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return None

def convert_stock_code_format(stock_codes):
    """转换股票代码格式，将市场前缀转换为后缀格式"""
    market_map = {
        # 沪市
        '20': '.SH',  # 沪市ETF、LOF等
        '17': '.SH',  # 沪市主板
        '19': '.SH',  # 沪市可转债
        '22': '.SH',  # 沪市B股
        
        # 深市
        '36': '.SZ',  # 深市ETF、LOF等
        '33': '.SZ',  # 深市主板
        '35': '.SZ',  # 深市可转债
        '39': '.SZ',  # 深市中小板
        
        # 港股
        '79': '.HK',  # 港股
        '76': '.HK',  # 港股
        
        # 美股
        '87': '.US',  # 美股
        '71': '.US',  # 美股纳斯达克
        '70': '.US',  # 美股其他
        
        # 指数
        '48': '.IDX',  # 行业指数
        '49': '.IDX',  # 概念指数
        '80': '.IDX',  # 指数
        '88': '.IDX',  # 国际指数
        
        # 其他
        '97': '.FX',   # 外汇
        '16': '.FD',   # 基金
    }
    
    converted_codes = []
    
    for code in stock_codes:
        if ':' in code:
            market_code, stock_code = code.split(':', 1)
            
            # 特殊处理港股，去掉HK前缀
            if market_code in ['79', '76'] and stock_code.startswith('HK'):
                stock_code = stock_code[2:]
            
            suffix = market_map.get(market_code, '')
            if suffix:
                converted_codes.append(f"{stock_code}{suffix}")
            else:
                converted_codes.append(code)
        else:
            converted_codes.append(code)
    
    return converted_codes

def get_stocks_by_block_name(block_name, ths_path, convert_format=True):
    """
    根据板块名称获取对应的股票代码列表
    
    Args:
        block_name: 板块名称，如"右则交易"
        ths_path: 同花顺安装目录路径，如"D:\\RJ\\10jqka\\同花顺\\mx_148114686"
        convert_format: 是否转换股票代码格式，默认为True
    
    Returns:
        股票代码列表，如果未找到板块则返回空列表
    """
    # 检查目录是否存在
    if not os.path.exists(ths_path):
        print(f"目录不存在: {ths_path}")
        return []
    
    # 拼接完整的文件路径
    file_path = os.path.join(ths_path, "StockBlock.ini")
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return []
    
    content = read_stock_block_ini(ths_path)
    if not content:
        return []
    
    # 解析板块名称映射表
    block_name_map = {}
    pattern = r'\[BLOCK_NAME_MAP_TABLE\](.*?)(?=\[|$)'
    match = re.search(pattern, content, re.DOTALL)
    
    if match:
        block_section = match.group(1).strip()
        for line in block_section.split('\n'):
            if '=' in line:
                code, name = line.split('=', 1)
                block_name_map[code.strip()] = name.strip()
    
    # 查找板块代码
    block_code = None
    for code, name in block_name_map.items():
        if name == block_name:
            block_code = code
            break
    
    if not block_code:
        print(f"未找到板块: {block_name}")
        return []
    
    # 解析板块股票内容
    block_stock_context = {}
    pattern = r'\[BLOCK_STOCK_CONTEXT\](.*?)(?=\[|$)'
    match = re.search(pattern, content, re.DOTALL)
    
    if match:
        stock_section = match.group(1).strip()
        for line in stock_section.split('\n'):
            if '=' in line:
                code, stocks = line.split('=', 1)
                stock_list = [s for s in stocks.strip().split(',') if s]
                block_stock_context[code.strip()] = stock_list
    
    stock_codes = block_stock_context.get(block_code, [])
    
    # 如果需要转换格式
    if convert_format and stock_codes:
        return convert_stock_code_format(stock_codes)
    
    return stock_codes

if __name__ == "__main__":
    # 同花顺安装目录路径
    ths_path = r"D:\RJ\10jqka\同花顺\mx_148114686"
    
    # 获取并打印指定板块的股票代码
    block_name = "右则交易"
    stocks = get_stocks_by_block_name(block_name, ths_path)
    print(f"板块 '{block_name}' 的股票代码:")
    print(stocks)


