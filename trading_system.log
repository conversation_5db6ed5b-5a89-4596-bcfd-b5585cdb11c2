2025-08-04 12:40:54,696 - __main__ - INFO - 🚀 启动同花顺交易程序...
2025-08-04 12:40:54,696 - __main__ - INFO - 📍 当前工作目录: C:\Users\<USER>\Desktop\a原版下载无修改168元购买--非加密 - QMT与同花顺结合
2025-08-04 12:40:54,696 - __main__ - INFO - 🔧 创建应用实例...
2025-08-04 12:40:54,778 - __main__ - INFO - 🔄 初始化当日交易跟踪系统...
2025-08-04 12:40:54,778 - __main__ - INFO - 📂 开始加载当日交易数据...
2025-08-04 12:40:54,781 - __main__ - INFO - ✅ 交易数据验证通过
2025-08-04 12:40:54,781 - __main__ - INFO - 📂 成功加载当日交易记录: 0 只股票
2025-08-04 12:40:54,781 - __main__ - INFO - 📂 当日交易数据加载完成
2025-08-04 12:40:54,782 - __main__ - INFO - 📅 开始检查日期变化...
2025-08-04 12:40:54,782 - __main__ - INFO - 📅 日期变化检查完成
2025-08-04 12:40:54,782 - __main__ - INFO - 当日交易跟踪系统初始化完成，已跟踪 0 只卖出股票
2025-08-04 12:40:54,782 - __main__ - INFO - 程序初始化完成，GUI界面已启动
2025-08-04 12:40:54,783 - __main__ - INFO - ℹ️ 程序已启动，GUI界面正在运行...
2025-08-04 12:40:58,083 - __main__ - INFO - 👋 程序已退出
2025-08-04 12:41:06,068 - __main__ - INFO - 🚀 启动同花顺交易程序...
2025-08-04 12:41:06,069 - __main__ - INFO - 📍 当前工作目录: C:\Users\<USER>\Desktop\a原版下载无修改168元购买--非加密 - QMT与同花顺结合
2025-08-04 12:41:06,069 - __main__ - INFO - 🔧 创建应用实例...
2025-08-04 12:41:06,154 - __main__ - INFO - 🔄 初始化当日交易跟踪系统...
2025-08-04 12:41:06,154 - __main__ - INFO - 📂 开始加载当日交易数据...
2025-08-04 12:41:06,156 - __main__ - INFO - ✅ 交易数据验证通过
2025-08-04 12:41:06,157 - __main__ - INFO - 📂 成功加载当日交易记录: 0 只股票
2025-08-04 12:41:06,157 - __main__ - INFO - 📂 当日交易数据加载完成
2025-08-04 12:41:06,157 - __main__ - INFO - 📅 开始检查日期变化...
2025-08-04 12:41:06,157 - __main__ - INFO - 📅 日期变化检查完成
2025-08-04 12:41:06,157 - __main__ - INFO - 当日交易跟踪系统初始化完成，已跟踪 0 只卖出股票
2025-08-04 12:41:06,157 - __main__ - INFO - 程序初始化完成，GUI界面已启动
2025-08-04 12:41:06,158 - __main__ - INFO - ℹ️ 程序已启动，GUI界面正在运行...
2025-08-04 12:43:38,213 - __main__ - ERROR - 保存当日交易记录失败: 'bool' object has no attribute 'get'
2025-08-04 12:43:38,217 - __main__ - ERROR - Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\a原版下载无修改168元购买--非加密 - QMT与同花顺结合\qmt_ths\tonghuashun_gui.py", line 2517, in on_focus_out_save
    self.save_config_silent()  # 使用静默保存方法
    ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\a原版下载无修改168元购买--非加密 - QMT与同花顺结合\qmt_ths\tonghuashun_gui.py", line 407, in save_config_silent
    'enable_filter': self.enable_sold_stock_filter.get(),
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'bool' object has no attribute 'get'

2025-08-04 12:43:44,810 - __main__ - ERROR - 保存当日交易记录失败: 'bool' object has no attribute 'get'
2025-08-04 12:43:44,811 - __main__ - ERROR - Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\a原版下载无修改168元购买--非加密 - QMT与同花顺结合\qmt_ths\tonghuashun_gui.py", line 2517, in on_focus_out_save
    self.save_config_silent()  # 使用静默保存方法
    ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\a原版下载无修改168元购买--非加密 - QMT与同花顺结合\qmt_ths\tonghuashun_gui.py", line 407, in save_config_silent
    'enable_filter': self.enable_sold_stock_filter.get(),
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'bool' object has no attribute 'get'

2025-08-04 12:43:45,708 - __main__ - ERROR - 保存当日交易记录失败: 'bool' object has no attribute 'get'
2025-08-04 12:43:45,709 - __main__ - ERROR - Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\a原版下载无修改168元购买--非加密 - QMT与同花顺结合\qmt_ths\tonghuashun_gui.py", line 2517, in on_focus_out_save
    self.save_config_silent()  # 使用静默保存方法
    ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\a原版下载无修改168元购买--非加密 - QMT与同花顺结合\qmt_ths\tonghuashun_gui.py", line 407, in save_config_silent
    'enable_filter': self.enable_sold_stock_filter.get(),
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'bool' object has no attribute 'get'

2025-08-04 12:43:48,012 - __main__ - ERROR - 保存当日交易记录失败: 'bool' object has no attribute 'get'
2025-08-04 12:43:48,013 - __main__ - ERROR - Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\a原版下载无修改168元购买--非加密 - QMT与同花顺结合\qmt_ths\tonghuashun_gui.py", line 2517, in on_focus_out_save
    self.save_config_silent()  # 使用静默保存方法
    ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\a原版下载无修改168元购买--非加密 - QMT与同花顺结合\qmt_ths\tonghuashun_gui.py", line 407, in save_config_silent
    'enable_filter': self.enable_sold_stock_filter.get(),
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'bool' object has no attribute 'get'

2025-08-04 12:43:51,957 - __main__ - ERROR - 保存当日交易记录失败: 'bool' object has no attribute 'get'
2025-08-04 12:43:51,958 - __main__ - ERROR - Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\a原版下载无修改168元购买--非加密 - QMT与同花顺结合\qmt_ths\tonghuashun_gui.py", line 2517, in on_focus_out_save
    self.save_config_silent()  # 使用静默保存方法
    ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\a原版下载无修改168元购买--非加密 - QMT与同花顺结合\qmt_ths\tonghuashun_gui.py", line 407, in save_config_silent
    'enable_filter': self.enable_sold_stock_filter.get(),
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'bool' object has no attribute 'get'

2025-08-04 12:53:38,323 - __main__ - INFO - 👋 程序已退出
2025-08-04 12:54:32,529 - __main__ - INFO - 🚀 启动同花顺交易程序...
2025-08-04 12:54:32,529 - __main__ - INFO - 📍 当前工作目录: C:\Users\<USER>\Desktop\a原版下载无修改168元购买--非加密 - QMT与同花顺结合
2025-08-04 12:54:32,529 - __main__ - INFO - 🔧 创建应用实例...
2025-08-04 12:54:32,623 - __main__ - INFO - 🔄 初始化当日交易跟踪系统...
2025-08-04 12:54:32,624 - __main__ - INFO - 📂 开始加载当日交易数据...
2025-08-04 12:54:32,626 - __main__ - INFO - ✅ 交易数据验证通过
2025-08-04 12:54:32,626 - __main__ - INFO - 📂 成功加载当日交易记录: 0 只股票
2025-08-04 12:54:32,626 - __main__ - INFO - 📂 当日交易数据加载完成
2025-08-04 12:54:32,626 - __main__ - INFO - 📅 开始检查日期变化...
2025-08-04 12:54:32,627 - __main__ - INFO - 📅 日期变化检查完成
2025-08-04 12:54:32,627 - __main__ - INFO - 当日交易跟踪系统初始化完成，已跟踪 0 只卖出股票
2025-08-04 12:54:32,627 - __main__ - INFO - 程序初始化完成，GUI界面已启动
2025-08-04 12:54:32,627 - __main__ - INFO - ℹ️ 程序已启动，GUI界面正在运行...
2025-08-04 12:54:49,486 - read_block - INFO - 成功读取StockBlock.ini文件: D:\Application\Stock\Ths\山海sM\StockBlock.ini
2025-08-04 12:54:49,557 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-04 12:54:49,602 - tzlocal - DEBUG - Looking up time zone info from registry
2025-08-04 12:54:49,610 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-04 12:54:49,610 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-04 12:54:49,610 - apscheduler.scheduler - INFO - Added job "TongHuaShunTrader.monitor_job" to job store "default"
2025-08-04 12:54:49,611 - apscheduler.scheduler - INFO - Added job "TongHuaShunTrader.cancel_orders_wrapper" to job store "default"
2025-08-04 12:54:49,611 - apscheduler.scheduler - INFO - Scheduler started
2025-08-04 12:54:49,611 - apscheduler.scheduler - DEBUG - Looking for jobs to run
2025-08-04 12:54:49,611 - apscheduler.scheduler - DEBUG - Next wakeup is due at 2025-08-04 12:54:50.609990+08:00 (in 0.998110 seconds)
2025-08-04 12:54:50,621 - apscheduler.scheduler - DEBUG - Looking for jobs to run
2025-08-04 12:54:50,622 - apscheduler.executors.default - INFO - Running job "TongHuaShunTrader.monitor_job (trigger: interval[0:00:01], next run at: 2025-08-04 12:54:50 CST)" (scheduled at 2025-08-04 12:54:50.609990+08:00)
2025-08-04 12:54:50,622 - apscheduler.scheduler - DEBUG - Next wakeup is due at 2025-08-04 12:54:51.609990+08:00 (in 0.987574 seconds)
2025-08-04 12:54:50,624 - read_block - INFO - 成功读取StockBlock.ini文件: D:\Application\Stock\Ths\山海sM\StockBlock.ini
2025-08-04 12:54:50,797 - apscheduler.executors.default - INFO - Job "TongHuaShunTrader.monitor_job (trigger: interval[0:00:01], next run at: 2025-08-04 12:54:51 CST)" executed successfully
2025-08-04 12:54:51,614 - apscheduler.scheduler - DEBUG - Looking for jobs to run
2025-08-04 12:54:51,614 - apscheduler.scheduler - DEBUG - Next wakeup is due at 2025-08-04 12:54:52.609990+08:00 (in 0.995292 seconds)
2025-08-04 12:54:51,614 - apscheduler.executors.default - INFO - Running job "TongHuaShunTrader.monitor_job (trigger: interval[0:00:01], next run at: 2025-08-04 12:54:52 CST)" (scheduled at 2025-08-04 12:54:51.609990+08:00)
2025-08-04 12:54:51,617 - read_block - INFO - 成功读取StockBlock.ini文件: D:\Application\Stock\Ths\山海sM\StockBlock.ini
2025-08-04 12:54:51,727 - apscheduler.executors.default - INFO - Job "TongHuaShunTrader.monitor_job (trigger: interval[0:00:01], next run at: 2025-08-04 12:54:52 CST)" executed successfully
2025-08-04 12:54:52,619 - apscheduler.scheduler - DEBUG - Looking for jobs to run
2025-08-04 12:54:52,619 - apscheduler.scheduler - DEBUG - Next wakeup is due at 2025-08-04 12:54:53.609990+08:00 (in 0.990052 seconds)
2025-08-04 12:54:52,620 - apscheduler.executors.default - INFO - Running job "TongHuaShunTrader.monitor_job (trigger: interval[0:00:01], next run at: 2025-08-04 12:54:53 CST)" (scheduled at 2025-08-04 12:54:52.609990+08:00)
2025-08-04 12:54:52,622 - read_block - INFO - 成功读取StockBlock.ini文件: D:\Application\Stock\Ths\山海sM\StockBlock.ini
2025-08-04 12:54:52,729 - apscheduler.executors.default - INFO - Job "TongHuaShunTrader.monitor_job (trigger: interval[0:00:01], next run at: 2025-08-04 12:54:53 CST)" executed successfully
2025-08-04 12:54:53,622 - apscheduler.scheduler - DEBUG - Looking for jobs to run
2025-08-04 12:54:53,622 - apscheduler.scheduler - DEBUG - Next wakeup is due at 2025-08-04 12:54:54.609990+08:00 (in 0.987490 seconds)
2025-08-04 12:54:53,622 - apscheduler.executors.default - INFO - Running job "TongHuaShunTrader.monitor_job (trigger: interval[0:00:01], next run at: 2025-08-04 12:54:54 CST)" (scheduled at 2025-08-04 12:54:53.609990+08:00)
2025-08-04 12:54:53,624 - read_block - INFO - 成功读取StockBlock.ini文件: D:\Application\Stock\Ths\山海sM\StockBlock.ini
2025-08-04 12:54:53,736 - apscheduler.executors.default - INFO - Job "TongHuaShunTrader.monitor_job (trigger: interval[0:00:01], next run at: 2025-08-04 12:54:54 CST)" executed successfully
2025-08-04 12:54:54,613 - apscheduler.scheduler - DEBUG - Looking for jobs to run
2025-08-04 12:54:54,613 - apscheduler.scheduler - DEBUG - Next wakeup is due at 2025-08-04 12:54:55.609990+08:00 (in 0.996529 seconds)
2025-08-04 12:54:54,613 - apscheduler.executors.default - INFO - Running job "TongHuaShunTrader.monitor_job (trigger: interval[0:00:01], next run at: 2025-08-04 12:54:55 CST)" (scheduled at 2025-08-04 12:54:54.609990+08:00)
2025-08-04 12:54:54,615 - read_block - INFO - 成功读取StockBlock.ini文件: D:\Application\Stock\Ths\山海sM\StockBlock.ini
2025-08-04 12:54:54,717 - apscheduler.executors.default - INFO - Job "TongHuaShunTrader.monitor_job (trigger: interval[0:00:01], next run at: 2025-08-04 12:54:55 CST)" executed successfully
2025-08-04 12:54:55,288 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-08-04 12:54:55,288 - apscheduler.scheduler - DEBUG - Looking for jobs to run
2025-08-04 12:54:55,289 - apscheduler.scheduler - DEBUG - No jobs; waiting until a job is added
2025-08-04 12:55:16,054 - __main__ - INFO - 👋 程序已退出
2025-08-04 12:58:10,822 - __main__ - INFO - 🚀 启动同花顺交易程序...
2025-08-04 12:58:10,823 - __main__ - INFO - 📍 当前工作目录: C:\Users\<USER>\Desktop\a原版下载无修改168元购买--非加密 - QMT与同花顺结合
2025-08-04 12:58:10,823 - __main__ - INFO - 🔧 创建应用实例...
2025-08-04 12:58:10,924 - __main__ - INFO - 🔄 初始化当日交易跟踪系统...
2025-08-04 12:58:10,924 - __main__ - INFO - 📂 开始加载当日交易数据...
2025-08-04 12:58:10,927 - __main__ - INFO - ✅ 交易数据验证通过
2025-08-04 12:58:10,927 - __main__ - INFO - 📂 成功加载当日交易记录: 0 只股票
2025-08-04 12:58:10,928 - __main__ - INFO - 📂 当日交易数据加载完成
2025-08-04 12:58:10,928 - __main__ - INFO - 📅 开始检查日期变化...
2025-08-04 12:58:10,928 - __main__ - INFO - 📅 日期变化检查完成
2025-08-04 12:58:10,928 - __main__ - INFO - 当日交易跟踪系统初始化完成，已跟踪 0 只卖出股票
2025-08-04 12:58:10,929 - __main__ - INFO - 程序初始化完成，GUI界面已启动
2025-08-04 12:58:10,929 - __main__ - INFO - ℹ️ 程序已启动，GUI界面正在运行...
2025-08-04 12:58:14,830 - read_block - INFO - 成功读取StockBlock.ini文件: D:\Application\Stock\Ths\山海sM\StockBlock.ini
2025-08-04 12:58:19,085 - read_block - INFO - 成功读取StockBlock.ini文件: D:\Application\Stock\Ths\山海sM\StockBlock.ini
2025-08-04 12:58:19,139 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-04 12:58:19,186 - tzlocal - DEBUG - Looking up time zone info from registry
2025-08-04 12:58:19,189 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-04 12:58:19,190 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-04 12:58:19,190 - apscheduler.scheduler - INFO - Added job "TongHuaShunTrader.monitor_job" to job store "default"
2025-08-04 12:58:19,190 - apscheduler.scheduler - INFO - Added job "TongHuaShunTrader.cancel_orders_wrapper" to job store "default"
2025-08-04 12:58:19,190 - apscheduler.scheduler - INFO - Scheduler started
2025-08-04 12:58:19,191 - apscheduler.scheduler - DEBUG - Looking for jobs to run
2025-08-04 12:58:19,191 - apscheduler.scheduler - DEBUG - Next wakeup is due at 2025-08-04 12:58:20.189631+08:00 (in 0.998330 seconds)
2025-08-04 12:58:20,193 - apscheduler.scheduler - DEBUG - Looking for jobs to run
2025-08-04 12:58:20,194 - apscheduler.executors.default - INFO - Running job "TongHuaShunTrader.monitor_job (trigger: interval[0:00:01], next run at: 2025-08-04 12:58:20 CST)" (scheduled at 2025-08-04 12:58:20.189631+08:00)
2025-08-04 12:58:20,194 - apscheduler.scheduler - DEBUG - Next wakeup is due at 2025-08-04 12:58:21.189631+08:00 (in 0.994848 seconds)
2025-08-04 12:58:20,197 - read_block - INFO - 成功读取StockBlock.ini文件: D:\Application\Stock\Ths\山海sM\StockBlock.ini
2025-08-04 12:58:20,455 - apscheduler.executors.default - INFO - Job "TongHuaShunTrader.monitor_job (trigger: interval[0:00:01], next run at: 2025-08-04 12:58:21 CST)" executed successfully
2025-08-04 12:58:21,199 - apscheduler.scheduler - DEBUG - Looking for jobs to run
2025-08-04 12:58:21,199 - apscheduler.scheduler - DEBUG - Next wakeup is due at 2025-08-04 12:58:22.189631+08:00 (in 0.989911 seconds)
2025-08-04 12:58:21,199 - apscheduler.executors.default - INFO - Running job "TongHuaShunTrader.monitor_job (trigger: interval[0:00:01], next run at: 2025-08-04 12:58:22 CST)" (scheduled at 2025-08-04 12:58:21.189631+08:00)
2025-08-04 12:58:21,201 - read_block - INFO - 成功读取StockBlock.ini文件: D:\Application\Stock\Ths\山海sM\StockBlock.ini
2025-08-04 12:58:21,263 - apscheduler.executors.default - INFO - Job "TongHuaShunTrader.monitor_job (trigger: interval[0:00:01], next run at: 2025-08-04 12:58:22 CST)" executed successfully
2025-08-04 12:58:22,193 - apscheduler.scheduler - DEBUG - Looking for jobs to run
2025-08-04 12:58:22,194 - apscheduler.scheduler - DEBUG - Next wakeup is due at 2025-08-04 12:58:23.189631+08:00 (in 0.995598 seconds)
2025-08-04 12:58:22,194 - apscheduler.executors.default - INFO - Running job "TongHuaShunTrader.monitor_job (trigger: interval[0:00:01], next run at: 2025-08-04 12:58:23 CST)" (scheduled at 2025-08-04 12:58:22.189631+08:00)
2025-08-04 12:58:22,195 - read_block - INFO - 成功读取StockBlock.ini文件: D:\Application\Stock\Ths\山海sM\StockBlock.ini
2025-08-04 12:58:22,260 - apscheduler.executors.default - INFO - Job "TongHuaShunTrader.monitor_job (trigger: interval[0:00:01], next run at: 2025-08-04 12:58:23 CST)" executed successfully
2025-08-04 12:58:23,192 - apscheduler.scheduler - DEBUG - Looking for jobs to run
2025-08-04 12:58:23,192 - apscheduler.scheduler - DEBUG - Next wakeup is due at 2025-08-04 12:58:24.189631+08:00 (in 0.996862 seconds)
2025-08-04 12:58:23,192 - apscheduler.executors.default - INFO - Running job "TongHuaShunTrader.monitor_job (trigger: interval[0:00:01], next run at: 2025-08-04 12:58:24 CST)" (scheduled at 2025-08-04 12:58:23.189631+08:00)
2025-08-04 12:58:23,195 - read_block - INFO - 成功读取StockBlock.ini文件: D:\Application\Stock\Ths\山海sM\StockBlock.ini
2025-08-04 12:58:23,263 - apscheduler.executors.default - INFO - Job "TongHuaShunTrader.monitor_job (trigger: interval[0:00:01], next run at: 2025-08-04 12:58:24 CST)" executed successfully
2025-08-04 12:58:24,199 - apscheduler.scheduler - DEBUG - Looking for jobs to run
2025-08-04 12:58:24,200 - apscheduler.scheduler - DEBUG - Next wakeup is due at 2025-08-04 12:58:25.189631+08:00 (in 0.989257 seconds)
2025-08-04 12:58:24,200 - apscheduler.executors.default - INFO - Running job "TongHuaShunTrader.monitor_job (trigger: interval[0:00:01], next run at: 2025-08-04 12:58:25 CST)" (scheduled at 2025-08-04 12:58:24.189631+08:00)
2025-08-04 12:58:24,202 - read_block - INFO - 成功读取StockBlock.ini文件: D:\Application\Stock\Ths\山海sM\StockBlock.ini
2025-08-04 12:58:24,265 - apscheduler.executors.default - INFO - Job "TongHuaShunTrader.monitor_job (trigger: interval[0:00:01], next run at: 2025-08-04 12:58:25 CST)" executed successfully
2025-08-04 12:58:24,784 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-08-04 12:58:24,784 - apscheduler.scheduler - DEBUG - Looking for jobs to run
2025-08-04 12:58:24,784 - apscheduler.scheduler - DEBUG - No jobs; waiting until a job is added
2025-08-04 12:58:35,252 - read_block - INFO - 成功读取StockBlock.ini文件: D:\Application\Stock\Ths\山海sM\StockBlock.ini
2025-08-04 12:58:35,269 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-04 12:58:35,308 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-04 12:58:35,308 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-04 12:58:35,308 - apscheduler.scheduler - INFO - Added job "TongHuaShunTrader.monitor_job" to job store "default"
2025-08-04 12:58:35,309 - apscheduler.scheduler - INFO - Added job "TongHuaShunTrader.cancel_orders_wrapper" to job store "default"
2025-08-04 12:58:35,309 - apscheduler.scheduler - INFO - Scheduler started
2025-08-04 12:58:35,309 - apscheduler.scheduler - DEBUG - Looking for jobs to run
2025-08-04 12:58:35,309 - apscheduler.scheduler - DEBUG - Next wakeup is due at 2025-08-04 12:58:36.308035+08:00 (in 0.998244 seconds)
2025-08-04 12:58:36,309 - apscheduler.scheduler - DEBUG - Looking for jobs to run
2025-08-04 12:58:36,310 - apscheduler.executors.default - INFO - Running job "TongHuaShunTrader.monitor_job (trigger: interval[0:00:01], next run at: 2025-08-04 12:58:36 CST)" (scheduled at 2025-08-04 12:58:36.308035+08:00)
2025-08-04 12:58:36,310 - apscheduler.scheduler - DEBUG - Next wakeup is due at 2025-08-04 12:58:37.308035+08:00 (in 0.997310 seconds)
2025-08-04 12:58:36,312 - read_block - INFO - 成功读取StockBlock.ini文件: D:\Application\Stock\Ths\山海sM\StockBlock.ini
2025-08-04 12:58:36,547 - apscheduler.executors.default - INFO - Job "TongHuaShunTrader.monitor_job (trigger: interval[0:00:01], next run at: 2025-08-04 12:58:37 CST)" executed successfully
2025-08-04 12:58:37,315 - apscheduler.scheduler - DEBUG - Looking for jobs to run
2025-08-04 12:58:37,315 - apscheduler.scheduler - DEBUG - Next wakeup is due at 2025-08-04 12:58:38.308035+08:00 (in 0.992466 seconds)
2025-08-04 12:58:37,315 - apscheduler.executors.default - INFO - Running job "TongHuaShunTrader.monitor_job (trigger: interval[0:00:01], next run at: 2025-08-04 12:58:38 CST)" (scheduled at 2025-08-04 12:58:37.308035+08:00)
2025-08-04 12:58:37,317 - read_block - INFO - 成功读取StockBlock.ini文件: D:\Application\Stock\Ths\山海sM\StockBlock.ini
2025-08-04 12:58:37,367 - apscheduler.executors.default - INFO - Job "TongHuaShunTrader.monitor_job (trigger: interval[0:00:01], next run at: 2025-08-04 12:58:38 CST)" executed successfully
2025-08-04 12:58:38,308 - apscheduler.scheduler - DEBUG - Looking for jobs to run
2025-08-04 12:58:38,308 - apscheduler.scheduler - DEBUG - Next wakeup is due at 2025-08-04 12:58:39.308035+08:00 (in 0.999530 seconds)
2025-08-04 12:58:38,308 - apscheduler.executors.default - INFO - Running job "TongHuaShunTrader.monitor_job (trigger: interval[0:00:01], next run at: 2025-08-04 12:58:39 CST)" (scheduled at 2025-08-04 12:58:38.308035+08:00)
2025-08-04 12:58:38,310 - read_block - INFO - 成功读取StockBlock.ini文件: D:\Application\Stock\Ths\山海sM\StockBlock.ini
2025-08-04 12:58:38,372 - apscheduler.executors.default - INFO - Job "TongHuaShunTrader.monitor_job (trigger: interval[0:00:01], next run at: 2025-08-04 12:58:39 CST)" executed successfully
2025-08-04 12:58:39,321 - apscheduler.scheduler - DEBUG - Looking for jobs to run
2025-08-04 12:58:39,322 - apscheduler.scheduler - DEBUG - Next wakeup is due at 2025-08-04 12:58:40.308035+08:00 (in 0.986002 seconds)
2025-08-04 12:58:39,322 - apscheduler.executors.default - INFO - Running job "TongHuaShunTrader.monitor_job (trigger: interval[0:00:01], next run at: 2025-08-04 12:58:40 CST)" (scheduled at 2025-08-04 12:58:39.308035+08:00)
2025-08-04 12:58:39,323 - read_block - INFO - 成功读取StockBlock.ini文件: D:\Application\Stock\Ths\山海sM\StockBlock.ini
2025-08-04 12:58:39,387 - apscheduler.executors.default - INFO - Job "TongHuaShunTrader.monitor_job (trigger: interval[0:00:01], next run at: 2025-08-04 12:58:40 CST)" executed successfully
2025-08-04 12:58:40,068 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-08-04 12:58:40,068 - apscheduler.scheduler - DEBUG - Looking for jobs to run
2025-08-04 12:58:40,069 - apscheduler.scheduler - DEBUG - No jobs; waiting until a job is added
2025-08-04 14:04:19,411 - __main__ - INFO - 👋 程序已退出
2025-08-04 14:04:20,233 - __main__ - INFO - 🚀 启动同花顺交易程序...
2025-08-04 14:04:20,233 - __main__ - INFO - 📍 当前工作目录: C:\Users\<USER>\Desktop\a原版下载无修改168元购买--非加密 - QMT与同花顺结合
2025-08-04 14:04:20,234 - __main__ - INFO - 🔧 创建应用实例...
2025-08-04 14:04:20,333 - __main__ - INFO - 🔄 初始化当日交易跟踪系统...
2025-08-04 14:04:20,333 - __main__ - INFO - 📂 开始加载当日交易数据...
2025-08-04 14:04:20,335 - __main__ - INFO - ✅ 交易数据验证通过
2025-08-04 14:04:20,335 - __main__ - INFO - 📂 成功加载当日交易记录: 0 只股票
2025-08-04 14:04:20,336 - __main__ - INFO - 📂 当日交易数据加载完成
2025-08-04 14:04:20,336 - __main__ - INFO - 📅 开始检查日期变化...
2025-08-04 14:04:20,336 - __main__ - INFO - 📅 日期变化检查完成
2025-08-04 14:04:20,336 - __main__ - INFO - 当日交易跟踪系统初始化完成，已跟踪 0 只卖出股票
2025-08-04 14:04:20,336 - __main__ - INFO - 程序初始化完成，GUI界面已启动
2025-08-04 14:04:20,337 - __main__ - INFO - ℹ️ 程序已启动，GUI界面正在运行...
2025-08-04 14:04:23,048 - __main__ - INFO - 👋 程序已退出
2025-08-04 14:04:23,840 - __main__ - INFO - 🚀 启动同花顺交易程序...
2025-08-04 14:04:23,840 - __main__ - INFO - 📍 当前工作目录: C:\Users\<USER>\Desktop\a原版下载无修改168元购买--非加密 - QMT与同花顺结合
2025-08-04 14:04:23,841 - __main__ - INFO - 🔧 创建应用实例...
2025-08-04 14:04:23,942 - __main__ - INFO - 🔄 初始化当日交易跟踪系统...
2025-08-04 14:04:23,942 - __main__ - INFO - 📂 开始加载当日交易数据...
2025-08-04 14:04:23,945 - __main__ - INFO - ✅ 交易数据验证通过
2025-08-04 14:04:23,945 - __main__ - INFO - 📂 成功加载当日交易记录: 0 只股票
2025-08-04 14:04:23,945 - __main__ - INFO - 📂 当日交易数据加载完成
2025-08-04 14:04:23,945 - __main__ - INFO - 📅 开始检查日期变化...
2025-08-04 14:04:23,945 - __main__ - INFO - 📅 日期变化检查完成
2025-08-04 14:04:23,946 - __main__ - INFO - 当日交易跟踪系统初始化完成，已跟踪 0 只卖出股票
2025-08-04 14:04:23,946 - __main__ - INFO - 程序初始化完成，GUI界面已启动
2025-08-04 14:04:23,946 - __main__ - INFO - ℹ️ 程序已启动，GUI界面正在运行...
2025-08-04 14:04:25,027 - __main__ - INFO - 👋 程序已退出
2025-08-04 14:04:25,761 - __main__ - INFO - 🚀 启动同花顺交易程序...
2025-08-04 14:04:25,761 - __main__ - INFO - 📍 当前工作目录: C:\Users\<USER>\Desktop\a原版下载无修改168元购买--非加密 - QMT与同花顺结合
2025-08-04 14:04:25,761 - __main__ - INFO - 🔧 创建应用实例...
2025-08-04 14:04:25,861 - __main__ - INFO - 🔄 初始化当日交易跟踪系统...
2025-08-04 14:04:25,862 - __main__ - INFO - 📂 开始加载当日交易数据...
2025-08-04 14:04:25,864 - __main__ - INFO - ✅ 交易数据验证通过
2025-08-04 14:04:25,864 - __main__ - INFO - 📂 成功加载当日交易记录: 0 只股票
2025-08-04 14:04:25,864 - __main__ - INFO - 📂 当日交易数据加载完成
2025-08-04 14:04:25,865 - __main__ - INFO - 📅 开始检查日期变化...
2025-08-04 14:04:25,865 - __main__ - INFO - 📅 日期变化检查完成
2025-08-04 14:04:25,865 - __main__ - INFO - 当日交易跟踪系统初始化完成，已跟踪 0 只卖出股票
2025-08-04 14:04:25,865 - __main__ - INFO - 程序初始化完成，GUI界面已启动
2025-08-04 14:04:25,865 - __main__ - INFO - ℹ️ 程序已启动，GUI界面正在运行...
2025-08-04 14:04:27,106 - __main__ - INFO - 👋 程序已退出
